package com.chinastock.bot.message.manager.impl;

import com.chinastock.bot.message.constant.AppConstants;
import com.chinastock.bot.message.enums.MessageDirEnum;
import com.chinastock.bot.message.manager.MessageManager;
import com.chinastock.bot.message.pojo.dto.CustMessage;
import com.chinastock.bot.message.pojo.vo.message.ReceiveMessageVo;
import com.chinastock.bot.message.util.DateUtils;
import com.chinastock.bot.message.util.IPUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Author: David
 * @Date: 2025/8/25 17:11
 * @Description:
 */
@Slf4j
@Service
public class MessageManagerImpl implements MessageManager {

    @Value("${message.check.open:true}")
    private boolean checkSwitch;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public CustMessage saveMessage(ReceiveMessageVo message, String depNo) {
        CustMessage custMessage = new CustMessage();
        try{
            custMessage.setId(message.getReceiveMessageData().getMessageId());
            custMessage.setSource("1");
            custMessage.setMessageId(message.getReceiveMessageData().getMessageId());
            custMessage.setChatId(message.getReceiveMessageData().getChatId());
            custMessage.setToken(message.getReceiveMessageData().getToken());
            custMessage.setDirection(MessageDirEnum.RECEIVE.getValue());
            custMessage.setGroupId(message.getReceiveMessageData().getRoomId());
            custMessage.setGroupName(message.getReceiveMessageData().getRoomTopic());
            custMessage.setDate(DateUtils.getCurrentDate());
            custMessage.setReceiveTime(DateUtils.dateToString(new Date(), DateUtils.TIME_FORMAT_LINE));
            custMessage.setTimestamp(message.getReceiveMessageData().getTimestamp());
            custMessage.setDeptNo(depNo);
            custMessage.setUserId(message.getReceiveMessageData().getContactId());
            custMessage.setUserName(message.getReceiveMessageData().getContactName());
            custMessage.setDelFlag("1");
            custMessage.setFromRobot(message.getReceiveMessageData().isSelf());
            custMessage.setData(message.getReceiveMessageData());
            custMessage.setLocalIp(IPUtils.getLocalIp());
            mongoTemplate.insert(custMessage, AppConstants.MESSAGE);
            return custMessage;
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error("saveMessage error. msg:{}", e.getMessage() + message, e);
            }
        }
        return custMessage;
    }

    @Override
    public boolean checkRepeat(String pre, String msgId) {
        if(!checkSwitch) {
            return false;
        }
        String key = AppConstants.BOT_MSG_REPEAT + ":" + pre + ":" + msgId;
        String value = stringRedisTemplate.opsForValue().get(key);
        if(value == null){
            stringRedisTemplate.opsForValue().set(key, "true",1, TimeUnit.HOURS);
            return false;
        }
        log.error("checkRepeat message repeat, msgId:{}", msgId);
        return true;
    }

    @Override
    public void delRepeat(String pre, String msgId) {
        if(!checkSwitch) {
            return;
        }
        String key = AppConstants.BOT_MSG_REPEAT + ":" + pre + ":" + msgId;
        stringRedisTemplate.delete(key);
    }
}
