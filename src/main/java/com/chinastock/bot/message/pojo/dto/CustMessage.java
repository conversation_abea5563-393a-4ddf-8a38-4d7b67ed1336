package com.chinastock.bot.message.pojo.dto;

import com.chinastock.bot.message.pojo.vo.message.ReceiveMessageData;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 消息来源，消息ID，会话ID，方向，群id，群名，接收时间，消息所属部门，发送人，软删除标识，原始消息data
 * @Author: David
 * @Date: 2025/8/26 16:36
 * @Description:
 */
@Data
public class CustMessage {

    @Field("_id")
    private String id;

    /**
     * 1为微信
     */
    private String source;

    private String messageId;

    private String chatId;

    private String token;

    /**
     * 1为发送，2为接收
     */
    private String direction;

    private String groupId;

    private String groupName;

    private Integer date;

    private String receiveTime;

    private Long timestamp;

    private String deptNo;

    private String userId;

    private String userName;

    /**
     * 1为正常，时间戳为删除
     */
    private String delFlag;

    private boolean fromRobot;

    private ReceiveMessageData data;

    private String localIp;

}
